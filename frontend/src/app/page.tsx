// Main Game Page - Session selection and game interface

'use client';

import React, { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { GameLayout } from '@/components/layout/GameLayout';
import { SessionManager } from '@/components/session/SessionManager';
import { useGameStore } from '@/stores/gameStore';
import { gameAPI } from '@/services/api/client';
import { QUERY_KEYS } from '@/utils/constants';
import { GameSession } from '@/types';

export default function Home() {
  const { currentSession, setCurrentSession } = useGameStore();

  // Fetch available sessions
  const { data: sessionsData } = useQuery({
    queryKey: QUERY_KEYS.SESSIONS,
    queryFn: () => gameAPI.getSessions(),
  });

  // Auto-select the first session if none is selected and sessions are available
  useEffect(() => {
    if (!currentSession && sessionsData?.sessions && sessionsData.sessions.length > 0) {
      console.log('Auto-selecting first available session:', sessionsData.sessions[0].session_id);
      setCurrentSession(sessionsData.sessions[0]);
    }
  }, [currentSession, sessionsData, setCurrentSession]);

  const handleSessionSelect = (session: GameSession) => {
    setCurrentSession(session);
  };

  // If no session is active, show session manager
  if (!currentSession) {
    return (
      <GameLayout>
        <SessionManager onSessionSelect={handleSessionSelect} />
      </GameLayout>
    );
  }

  // If session is active, let MainContent handle panel navigation
  return (
    <GameLayout>
      {/* MainContent will automatically render the appropriate panels */}
    </GameLayout>
  );
}
